# 使用指南

> 本文档已迁移到新的文档结构中，请访问 [docs/user-guide/usage-guide.md](docs/user-guide/usage-guide.md) 查看最新版本。

## 应用概览

这个演示应用展示了5个流行的JavaScript图片编辑库的核心功能。每个库都有独立的演示页面，您可以通过导航栏切换不同的库进行体验。

## 功能说明

### 通用功能

所有演示页面都包含以下核心功能：

1. **图片裁剪** - 支持自由裁剪和按比例裁剪
2. **图片旋转** - 支持任意角度旋转  
3. **亮度调节** - 调整图片亮度（-1 到 +1）
4. **对比度调节** - 调整图片对比度（-1 到 +1）
5. **文件操作** - 加载本地图片、下载编辑结果

### 各库特色功能

#### TUI Image Editor
- 完整的UI界面，开箱即用
- 支持文本添加、形状绘制
- 多种内置滤镜效果
- 支持图标和贴纸添加

#### Fabric.js  
- 高度可定制的Canvas操作
- 支持复杂的图形变换
- 实时预览和交互
- 支持多对象操作

#### Cropper.js
- 专业的图片裁剪功能
- 支持多种裁剪比例
- 精确的裁剪控制
- 实时裁剪预览

#### Jimp
- 纯JavaScript实现
- 适合服务端处理
- 多种图像滤镜
- 支持批量处理

#### Konva.js
- 高性能2D图形渲染
- 丰富的动画效果
- 完善的事件系统
- 支持图层管理

## 操作步骤

### 基本操作流程

1. **选择库** - 在首页点击相应的库卡片，或使用顶部导航
2. **加载图片** - 点击"加载图片"按钮选择本地图片文件
3. **编辑图片** - 使用右侧控制面板调整各种参数
4. **预览效果** - 实时查看编辑效果
5. **下载结果** - 点击"下载图片"保存编辑后的图片

### 具体操作说明

#### 亮度和对比度调节
- 使用滑块调节数值
- 实时预览效果
- 支持负值（降低）和正值（增加）

#### 图片旋转
- 使用角度滑块精确控制
- 快速旋转按钮（90度）
- 支持任意角度旋转

#### 图片裁剪
- 启用裁剪模式
- 调整裁剪区域
- 应用裁剪效果

## 浏览器兼容性

- Chrome 60+
- Firefox 55+  
- Safari 12+
- Edge 79+

## 性能建议

- 建议图片大小不超过5MB
- 大图片可能影响处理速度
- 移动设备上建议使用较小的图片

## 常见问题

### Q: 为什么某些功能在移动设备上表现不同？
A: 不同的库对移动设备的支持程度不同，Jimp主要面向服务端，在移动浏览器中功能有限。

### Q: 如何保存编辑后的图片？
A: 点击"下载图片"按钮，浏览器会自动下载编辑后的图片文件。

### Q: 支持哪些图片格式？
A: 支持常见的图片格式：JPG、PNG、GIF、SVG等。

### Q: 编辑操作可以撤销吗？
A: 大部分库支持重置功能，可以恢复到原始状态。

## 技术细节

### 依赖版本
- tui-image-editor: ^3.15.3
- fabric: ^5.3.0  
- cropperjs: ^1.6.1
- jimp: ^0.22.10
- konva: ^9.2.0

### 构建信息
- Vue.js 2.6
- Webpack 构建
- 响应式设计
- ES6+ 语法

## 开发者信息

如需了解更多技术细节或进行二次开发，请参考各个库的官方文档：

- [TUI Image Editor](https://ui.toast.com/tui-image-editor/)
- [Fabric.js](http://fabricjs.com/)
- [Cropper.js](https://fengyuanchen.github.io/cropperjs/)
- [Jimp](https://github.com/jimp-dev/jimp)
- [Konva.js](https://konvajs.org/)
